from __future__ import annotations

import typing
import uuid
from inspect import cleandoc

import zangar as z
from django.db.models import Model, OuterRef, Prefetch, Q, Subquery, Sum
from django.db.models.query import QuerySet
from django.http import Http404, JsonResponse

import openapi
from wallpapers import models
from wallpapers.auth import s_authenticate
from wallpapers.utils import numfilter

from .base import APIView
from .common import s_in_topic, s_query_image_sizes
from .schemas import (
    clienttopic_struct,
    sql_integer_schema,
    uploader_struct,
    wallpaper_struct,
)


if typing.TYPE_CHECKING:
    from django.db.models.query import ValuesQuerySet


def _paging_response_schema(item_schema: z.Schema, /):
    return z.struct(
        {
            "results": z.to.list(item_schema),
            "page": z.int().gte(1),
            "page_size": z.int().gte(0),
            "total": z.int().gte(0),
        }
    )


M = typing.TypeVar("M", bound=Model)


@typing.overload
def _paginate(queryset: QuerySet[M], page: int, page_size: int) -> QuerySet[M]: ...


@typing.overload
def _paginate(
    queryset: ValuesQuerySet[M, dict[str, typing.Any]], page: int, page_size: int
) -> ValuesQuerySet[M, dict[str, typing.Any]]: ...


def _paginate(queryset, page: int, page_size: int):
    return queryset[(page - 1) * page_size : page * page_size]


def _paging_response_format(results, page: int, page_size: int, total: int):
    return {
        "results": results,
        "page": page,
        "page_size": page_size,
        "total": total,
    }


def _paging_response(queryset: QuerySet[M], page: int, page_size: int):
    return _paging_response_format(
        _paginate(queryset, page, page_size),
        page,
        page_size,
        queryset.count(),
    )


s_query_page = openapi.s_query(
    name="page", schema=z.to.int().gte(1).lte(1000), py_default=1
)
s_query_page_size = openapi.s_query(
    name="page_size", schema=z.to.int().gte(0).lte(1000), py_default=10
)
s_path_client_id = openapi.s_path(
    name="client_id", schema=z.str().transform(lambda x: uuid.UUID(x, version=4))
)
s_path_wallpaper_id = openapi.s_path(
    name="wallpaper_id", schema=z.to.int().relay(sql_integer_schema)
)
s_path_topic_id = openapi.s_path(
    name="topic_id", schema=z.to.int().relay(sql_integer_schema)
)
s_aspect_ratio = openapi.s_query(
    name="aspect_ratio",
    schema=z.list(z.str().transform(numfilter.parse_constraints)),
    py_default=None,
    description=cleandoc(
        """
        根据图片宽高比进行过滤，格式为 `>=1,<2.3`，表示宽高比大于等于 1 且小于 2.3。

        可以提供多个过滤条件，多个条件之间是 OR 关系。
    """
    ),
)


def _apply_aspect_ratio(
    queryset: QuerySet[models.WallpaperImage],
    aspect_ratio: list[list[numfilter.Constraint]],
):
    # 构建 OR 查询：每组之间是 OR 关系，组内是 AND 关系
    q = Q()

    for constraint_group in aspect_ratio:
        # 每个组内的约束是 AND 关系
        group_q = Q()

        for constraint in constraint_group:
            if constraint.operator == numfilter.Operator.EQ:
                group_q &= Q(aspect_ratio=constraint.value)
            elif constraint.operator == numfilter.Operator.GTE:
                group_q &= Q(aspect_ratio__gte=constraint.value)
            elif constraint.operator == numfilter.Operator.LTE:
                group_q &= Q(aspect_ratio__lte=constraint.value)
            elif constraint.operator == numfilter.Operator.GT:
                group_q &= Q(aspect_ratio__gt=constraint.value)
            elif constraint.operator == numfilter.Operator.LT:
                group_q &= Q(aspect_ratio__lt=constraint.value)

        q |= group_q

    return queryset.filter(q)


class WallpaperListAPI(APIView):
    __get_response_schema = _paging_response_schema(wallpaper_struct)

    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    @openapi.declare(tags=["rest"])
    @openapi.apply_signature
    def get(
        self,
        request,
        page=s_query_page,
        page_size=s_query_page_size,
        sizes=s_query_image_sizes,
        aspect_ratio=s_aspect_ratio,
        user=s_authenticate(required=False),
        in_topic=s_in_topic,
        include=openapi.s_query(
            name="include",
            schema=z.list(
                z.str().ensure(
                    lambda x: x in ["colors"],
                    meta={"oas": {"enum": ["colors"]}},
                )
            ),
            py_default=None,
        ),
    ):
        queryset = models.WallpaperImage.objects.all().order_by("-pk")
        if aspect_ratio:
            queryset = _apply_aspect_ratio(queryset, aspect_ratio)

        if in_topic is not None:
            if in_topic:
                queryset = queryset.filter(topicwallpaper__isnull=False)
            else:
                queryset = queryset.filter(topicwallpaper__isnull=True)

        if include and "colors" in include:
            queryset = queryset.prefetch_related(
                Prefetch("kmeanscolorcluster_set", to_attr="colors")
            )

        results = []
        for item in _paginate(queryset, page, page_size):
            if sizes:
                item.set_images(sizes)
            results.append(item)

        return JsonResponse(
            self.__get_response_schema.parse(
                _paging_response_format(results, page, page_size, queryset.count())
            )
        )


class WallpaperUploaderListAPI(APIView):
    __get_response_schema = _paging_response_schema(uploader_struct)

    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    @openapi.declare(tags=["rest"])
    @openapi.apply_signature
    def get(
        self,
        request,
        wallpaper_id=s_path_wallpaper_id,
        page=s_query_page,
        page_size=s_query_page_size,
    ):
        queryset = models.Uploader.objects.filter(
            uploaderwallpaper__wallpaper_id=wallpaper_id
        )
        return JsonResponse(
            self.__get_response_schema.parse(
                _paging_response(queryset, page, page_size)
            )
        )


class TopicWallpaperListAPI(APIView):
    __get_response_schema = _paging_response_schema(wallpaper_struct)

    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    @openapi.declare(tags=["rest"])
    @openapi.apply_signature
    def get(
        self,
        request,
        topic_id=s_path_topic_id,
        page=s_query_page,
        page_size=s_query_page_size,
        sizes=s_query_image_sizes,
        user=s_authenticate(required=False),
    ):
        queryset = models.WallpaperImage.objects.filter(
            topicwallpaper__topic_id=topic_id
        ).order_by("-pk")
        results = []
        for item in _paginate(queryset, page, page_size):
            if sizes:
                item.set_images(sizes)
            results.append(item)
        return JsonResponse(
            self.__get_response_schema.parse(
                _paging_response_format(results, page, page_size, queryset.count())
            )
        )


class ClientClienttopicListAPI(APIView):
    __get_response_schema = _paging_response_schema(clienttopic_struct)

    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    @openapi.declare(tags=["rest"])
    @openapi.apply_signature
    def get(
        self,
        request,
        client_id=s_path_client_id,
        page=s_query_page,
        page_size=s_query_page_size,
        include=openapi.s_query(
            name="include",
            schema=z.list(
                z.str().ensure(
                    lambda x: x in ["topic"], meta={"oas": {"enum": ["topic"]}}
                )
            ),
            py_default=None,
        ),
    ):
        queryset = models.ClientTopic.objects.filter(client_id=client_id).order_by(
            "-pk"
        )
        if include and "topic" in include:
            queryset = queryset.select_related("topic")
        return JsonResponse(
            self.__get_response_schema.parse(
                _paging_response(queryset, page, page_size)
            )
        )


class WallpaperSimilarAPI(APIView):
    __get_response_schema = z.struct(
        {
            "results": z.to.list(
                z.struct(
                    {
                        "wallpaper": wallpaper_struct,
                        "score": z.float(),
                    }
                ),
            ),
            "limit": z.int().gte(1),
            "offset": z.int().gte(0),
            "has_next": z.bool(meta={"oas": {"description": "是否还有下一页"}}),
        }
    )

    @openapi.declare(tags=["rest"])
    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    @openapi.apply_signature
    def get(
        self,
        request,
        wallpaper_id=s_path_wallpaper_id,
        sizes=s_query_image_sizes,
        limit=openapi.s_query(schema=z.to.int().gte(1).lte(100), py_default=10),
        offset=openapi.s_query(schema=z.to.int().gte(0).lte(1000), py_default=0),
        score_threshold=openapi.s_query(schema=z.to.float(), py_default=None),
        aspect_ratio=s_aspect_ratio,
        user=s_authenticate(required=False),
    ):
        from wallpapers.services import qdrant

        try:
            wallpaper = models.WallpaperImage.objects.get(pk=wallpaper_id)
        except models.WallpaperImage.DoesNotExist:
            raise Http404

        results, has_next = qdrant.get_similar_wallpapers(
            wallpaper,
            limit=limit,
            offset=offset,
            score_threshold=score_threshold,
            aspect_ratio=aspect_ratio,
        )

        if sizes:
            for item in results:
                item[0].set_images(sizes)

        return JsonResponse(
            self.__get_response_schema.parse(
                {
                    "results": [
                        {"wallpaper": wp, "score": score} for wp, score in results
                    ],
                    "limit": limit,
                    "offset": offset,
                    "has_next": has_next,
                }
            )
        )


class WallpaperColorSearchAPI(APIView):
    __get_response_schema = _paging_response_schema(wallpaper_struct)

    @openapi.declare(tags=["rest"])
    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    @openapi.apply_signature
    def get(
        self,
        request,
        rgb: tuple[int, ...] = openapi.s_path(
            schema=z.str(meta={"oas": {"example": "255,0,0"}})
            .transform(lambda x: x.split(","))
            .ensure(lambda x: len(x) == 3, message="RGB 颜色必须为 3 个值")
            .transform(lambda x: tuple(int(c) for c in x), message="RGB 值必须为整数")
            .ensure(
                lambda x: all(0 <= c <= 255 for c in x),
                message="RGB 值必须在 0~255 之间",
            )
        ),
        page=s_query_page,
        page_size=s_query_page_size,
        delta: int = openapi.s_query(
            schema=z.to.int(meta={"oas": {"default": 20}}).gte(0).lte(255),
            py_default=20,
        ),
        min_proportion: float = openapi.s_query(
            schema=z.to.float(meta={"oas": {"default": 0.6}}).gte(0).lte(1),
            py_default=0.6,
        ),
    ):
        r, g, b = rgb

        # 构建基础查询
        if delta == 0:
            color_filter = Q(r=r, g=g, b=b)
        else:
            color_filter = Q(
                r__gte=r - delta,
                r__lte=r + delta,
                g__gte=g - delta,
                g__lte=g + delta,
                b__gte=b - delta,
                b__lte=b + delta,
            )

        # 优化的聚合查询：使用子查询避免重复计算
        # 子查询：计算每个壁纸的总颜色占比
        color_proportion_subquery = Subquery(
            models.KMeansColorCluster.objects.filter(color_filter)
            .filter(wallpaper_id=OuterRef("pk"))
            .values("wallpaper_id")
            .annotate(total=Sum("proportion"))
            .values("total")[:1]
        )

        # 主查询：直接从 WallpaperImage 开始，避免 N+1 查询
        wallpapers_qs = (
            models.WallpaperImage.objects.annotate(
                color_proportion=color_proportion_subquery
            )
            .filter(color_proportion__gte=min_proportion)
            .order_by("-color_proportion")
        )

        # 分页和计数优化
        total_count = wallpapers_qs.count()
        wallpapers = _paginate(wallpapers_qs, page, page_size)

        return JsonResponse(
            self.__get_response_schema.parse(
                _paging_response_format(
                    wallpapers,
                    page,
                    page_size,
                    total_count,
                )
            )
        )
